import time
from datetime import datetime, date
from typing import List, Dict, Set
from collections import defaultdict

from models.models import AccountTrafficMetricsSnapshot, UserDailyRevenue, PromotionTaskDetail
from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler


async def execute_task() -> None:
    """
    计算用户每日任务收益

    逻辑：
    1. 获取今天和昨天的 AccountTrafficMetricsSnapshot 数据
    2. 计算每个任务的增量流量（今天的浏览量 - 昨天的浏览量）
    3. 根据增量流量计算收益（增量流量 * 0.001元）
    4. 创建或更新 UserDailyRevenue 记录
    """
    olog.info("开始执行用户每日任务收益计算")

    # 获取今天和昨天的零点时间戳
    today = date.today()
    today_timestamp = int(datetime.combine(today, datetime.min.time()).timestamp())
    yesterday_timestamp = today_timestamp - (24 * 60 * 60)

    olog.debug(f"今天零点时间戳: {today_timestamp}, 昨天零点时间戳: {yesterday_timestamp}")

    # 查询昨天和今天的快照数据
    yesterday_snapshots = await AccountTrafficMetricsSnapshot.find(
        AccountTrafficMetricsSnapshot.snapshot_at >= yesterday_timestamp,
        AccountTrafficMetricsSnapshot.snapshot_at < today_timestamp
    ).to_list()

    today_snapshots = await AccountTrafficMetricsSnapshot.find(
        AccountTrafficMetricsSnapshot.snapshot_at >= today_timestamp
    ).to_list()

    olog.debug(f"查询到昨天快照数据: {len(yesterday_snapshots)} 条")
    olog.debug(f"查询到今天快照数据: {len(today_snapshots)} 条")

    # 按 promotion_task_detail_id 分组快照数据
    yesterday_data: Dict[str, AccountTrafficMetricsSnapshot] = {}
    today_data: Dict[str, AccountTrafficMetricsSnapshot] = {}

    for snapshot in yesterday_snapshots:
        if snapshot.promotion_task_detail_id:
            yesterday_data[snapshot.promotion_task_detail_id] = snapshot

    for snapshot in today_snapshots:
        if snapshot.promotion_task_detail_id:
            today_data[snapshot.promotion_task_detail_id] = snapshot

    olog.debug(f"昨天有效快照数据: {len(yesterday_data)} 条")
    olog.debug(f"今天有效快照数据: {len(today_data)} 条")

    # 计算增量流量和收益
    processed_count = 0
    total_revenue = 0

    # 获取所有需要处理的 promotion_task_detail_id
    all_task_detail_ids = set(yesterday_data.keys()) | set(today_data.keys())

    for task_detail_id in all_task_detail_ids:
        yesterday_snapshot = yesterday_data.get(task_detail_id)
        today_snapshot = today_data.get(task_detail_id)

        # 如果今天没有数据，跳过
        if not today_snapshot:
            continue

        # 计算增量流量
        yesterday_views = yesterday_snapshot.view_count if yesterday_snapshot and yesterday_snapshot.view_count else 0
        today_views = today_snapshot.view_count if today_snapshot.view_count else 0

        incremental_views = max(0, today_views - yesterday_views)  # 确保增量不为负数

        # 计算收益（增量流量 * 0.001元，转换为分）
        daily_revenue = int(incremental_views * 0.001 * 100)  # 转换为分

        if daily_revenue <= 0:
            continue  # 跳过没有收益的记录

        # 获取用户ID
        # 需要通过 PromotionTaskDetail 获取 user_id
        task_detail = await PromotionTaskDetail.find_one(
            PromotionTaskDetail.id == task_detail_id
        )

        if not task_detail or not task_detail.user_id:
            olog.warning(f"任务详情 {task_detail_id} 不存在或没有关联用户，跳过")
            continue

        # 检查是否已存在今天的收益记录
        existing_revenue = await UserDailyRevenue.find_one(
            UserDailyRevenue.user_id == task_detail.user_id,
            UserDailyRevenue.promotion_task_detail_id == task_detail_id,
            UserDailyRevenue.date == today_timestamp
        )

        current_time = int(time.time())

        if existing_revenue:
            # 更新已存在的记录
            await existing_revenue.update({
                "$set": {
                    "daily_revenue": daily_revenue,
                    "status": "未结算",
                    "settled_at": None
                }
            })
            olog.debug(f"更新用户 {task_detail.user_id} 任务 {task_detail_id} 的收益记录: {daily_revenue}分")
        else:
            # 创建新的收益记录
            new_revenue = UserDailyRevenue(
                user_id=task_detail.user_id,
                promotion_task_detail_id=task_detail_id,
                date=today_timestamp,
                daily_revenue=daily_revenue,
                status="未结算",
                settled_at=None,
                created_at=current_time
            )
            await new_revenue.insert()
            olog.debug(f"创建用户 {task_detail.user_id} 任务 {task_detail_id} 的收益记录: {daily_revenue}分")

        processed_count += 1
        total_revenue += daily_revenue

        olog.debug(f"任务 {task_detail_id}: 昨天浏览量={yesterday_views}, 今天浏览量={today_views}, 增量={incremental_views}, 收益={daily_revenue}分")

    olog.info(f"用户每日任务收益计算完成，处理了 {processed_count} 条记录，总收益: {total_revenue}分 ({total_revenue/100:.2f}元)")


@register_scheduler(trigger="cron", hour="7", minute="0")
class UserDailyRevenueScheduler(BaseScheduler):
    async def run_task(self) -> None:
        """
        执行每日任务收益计算任务
        
        调度频率：每天早上7点执行一次
        """
        olog.info("开始执行每日任务收益计算任务")
        await execute_task()
        olog.info("每日任务收益计算任务执行完毕")


if __name__ == "__main__":
    import asyncio


    async def main():
        await init_models()
        await execute_task()


    asyncio.run(main())
